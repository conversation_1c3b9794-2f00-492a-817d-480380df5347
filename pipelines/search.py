from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.data_qa_prompt import data_qa_sys_prompt, data_qa_user_prompt, data_qa_rerank_prompt
from services.search_service import SearchService
from services.rerank_service import RerankService

from loguru import logger
from config.logging_config import configure_logging
from config.all_search_config import ALL_SEARCH_COLLECTIONS, ALL_SEARCH_MODEL_CONFIG, ALL_SEARCH_RERANK_CONFIG, DATA_SEARCH_COLLECTIONS, HARDWARE_SEARCH_COLLECTIONS, CAR_SEARCH_COLLECTIONS, get_collections_by_types
from config.isc_search_config import ISC_SEARCH_COLLECTIONS
configure_logging()

import json
import asyncio
 
class SEARCH:
    """RAG问答类，支持流式和非流式输出"""
    
    def __init__(self, request_id: str = None):
        """
        初始化RAG问答实例
        
        Args:
            request_id: 可选请求ID
        """
        self.request_id = request_id
        self.search_service = SearchService(config = ALL_SEARCH_MODEL_CONFIG, request_id=request_id, knowledge="knowledge")
        self.rerank_service = RerankService(config = ALL_SEARCH_RERANK_CONFIG, request_id=request_id)
        self.logger = logger.bind(request_id=request_id)

    def format_retrieved_docs(self, retrieved_docs: List[Any]) -> List[Any]:
        """格式化检索到的文档"""
        formated_retrieved_docs = []
        for doc in retrieved_docs:
            # print(f"doc: {doc}")
            data = {
                "title": "",
                "content": "",
                "docName": "",
                "docUrl": "",
                "sheetName": "",
                "owner": "",
                "update_time": "",
                "publish_time": "",
                "doc_type": "",
                "project_area": ""
            }
            docUrl1 = doc.get("metadata_json", {}).get("doc_url", "")
            docUrl2 = doc.get("metadata_json", {}).get("url", "")
            docUrl = docUrl1 if docUrl1 else docUrl2
            docUrl = str(docUrl)
            # print(f"docUrl: {docUrl}")
            title1 = doc.get("metadata_json", {}).get("doc_name", "")
            title2 = doc.get("metadata_json", {}).get("title", "")
            # print(f"title1: {title1}")
            # print(f"title2: {title2}")
            title = title1 if title1 else title2
            title = str(title)
            # print(f"title: {title}")
            data.update({"title": title})
            data.update({"content": doc.get("content", "")})
            data.update({"docName": title})
            data.update({"docUrl": docUrl})
            data.update({"owner": doc.get("metadata_json", {}).get("owner", "")})
            data.update({"update_time": doc.get("metadata_json", {}).get("update_time", "")})
            data.update({"publish_time": doc.get("metadata_json", {}).get("publish_time", "")})
            data.update({"project_area": doc.get("metadata_json", {}).get("project_area", "")})
            data.update({"doc_type": doc.get("metadata_json", {}).get("doc_type", "")})
            docType = doc.get("metadata_json", {}).get("doc_type", "")
            if "xiaomi.f.mioffice.cn"in docUrl and docType in ["doc", "sheet"]:
                sheetName = doc.get("metadata_json", {}).get("Header 2", "")
                data.update({"sheetName": sheetName})
            formated_retrieved_docs.append(data)
            # print(f"data: {data}")
        return formated_retrieved_docs

    async def _retrieve_knowledge(self, query: str, user_id: str, collection_name: str, top_k: int = None, min_score: float = None):
        """检索单个库并重排，不做top_r过滤，返回包含耗时信息的结果"""
        self.logger.info(f"检索库: {collection_name}, 用户ID: {user_id}, 查询: {query}, top_k: {top_k}, min_score: {min_score}")

        # 记录检索开始时间
        search_start_time = time.time()
        search_results, error = await self.search_service.search(
            user_id=user_id,
            query=query,
            top_k=top_k,
            collection_name=collection_name
        )
        # 计算检索耗时
        search_elapsed_time = time.time() - search_start_time
        print(f"search_results: {search_results}")
        if error or not search_results:
            return [], search_elapsed_time, 0.0
        self.logger.info(f"库 {collection_name} 检索到知识: {len(search_results)} 条，检索耗时: {search_elapsed_time:.3f}秒")

        # 记录重排开始时间
        rerank_start_time = time.time()
        # 重排知识 - 使用DATAQA专门的rerank prompt
        reranked_docs = await self.rerank_service.rerank_with_prompt(
            query=query,
            documents=search_results,
            instruction=data_qa_rerank_prompt,
            top_r=top_k,
            min_score=min_score
        )
        # 计算重排耗时
        rerank_elapsed_time = time.time() - rerank_start_time
        print(f"reranked_docs: {reranked_docs}")
        self.logger.info(f"库 {collection_name} 重排后知识: {len(reranked_docs)} 条，重排耗时: {rerank_elapsed_time:.3f}秒")
        # 格式化重排后的文档
        format_reranked_docs = self.format_retrieved_docs(reranked_docs)
        self.logger.info(f"库 {collection_name} 格式化完成，共获取到 {len(format_reranked_docs)} 个文档")
        return format_reranked_docs, search_elapsed_time, rerank_elapsed_time

    async def _retrieve_and_rerank_all_collections(self, query: str, user_id: str, collections, top_k: int = None, top_r: int = None, min_score: float = None):
        """异步检索指定collections并重排，按分组返回每组前20条，包含耗时信息"""
        tasks = []
        collection_names = []
        for collection in collections:
            collection_name = collection.get("collection_name")
            if collection_name:
                tasks.append(self._retrieve_knowledge(query, user_id, collection_name, top_k=top_k, min_score=min_score))
                collection_names.append(collection_name)
        all_results = await asyncio.gather(*tasks)

        # 合并结果，按集合类型分组，并收集耗时信息
        data_collections = set([c["collection_name"] for c in DATA_SEARCH_COLLECTIONS])
        hardware_collections = set([c["collection_name"] for c in HARDWARE_SEARCH_COLLECTIONS])
        car_collections = set([c["collection_name"] for c in CAR_SEARCH_COLLECTIONS])
        isc_collections = set([c["collection_name"] for c in ISC_SEARCH_COLLECTIONS])

        data_group = []
        hardware_group = []
        car_group = []
        isc_group = []

        # 收集各组的耗时信息
        data_search_times = []
        data_rerank_times = []
        hardware_search_times = []
        hardware_rerank_times = []
        car_search_times = []
        car_rerank_times = []
        isc_search_times = []
        isc_rerank_times = []

        for collection_name, result in zip(collection_names, all_results):
            docs, search_time, rerank_time = result
            if collection_name in data_collections:
                data_group.extend(docs)
                data_search_times.append(search_time)
                data_rerank_times.append(rerank_time)
                print(f"{collection_name}: {len(docs)} docs, {search_time:.2f}s, {rerank_time:.2f}s")
            elif collection_name in hardware_collections:
                hardware_group.extend(docs)
                hardware_search_times.append(search_time)
                hardware_rerank_times.append(rerank_time)
                print(f"{collection_name}: {len(docs)} docs, {search_time:.2f}s, {rerank_time:.2f}s")
            elif collection_name in car_collections:
                car_group.extend(docs)
                car_search_times.append(search_time)
                car_rerank_times.append(rerank_time)
                print(f"{collection_name}: {len(docs)} docs, {search_time:.2f}s, {rerank_time:.2f}s")
            elif collection_name in isc_collections:
                isc_group.extend(docs)
                isc_search_times.append(search_time)
                isc_rerank_times.append(rerank_time)
                print(f"{collection_name}: {len(docs)} docs, {search_time:.2f}s, {rerank_time:.2f}s")

        # 按score排序，每组取前20
        data_group.sort(key=lambda x: x.get("score", 0), reverse=True)
        hardware_group.sort(key=lambda x: x.get("score", 0), reverse=True)
        car_group.sort(key=lambda x: x.get("score", 0), reverse=True)
        isc_group.sort(key=lambda x: x.get("score", 0), reverse=True)

        # 构建结果，只包含有数据的集合，并添加耗时信息
        res = []
        if hardware_group:
            res.append({
                "collection": "hardwareKnowledge",
                "refs": hardware_group[:top_r],
                "search_time": max(hardware_search_times),
                "rerank_time": max(hardware_rerank_times)
            })
        if data_group:
            res.append({
                "collection": "rDataQuery",
                "refs": data_group[:top_r],
                "search_time": max(data_search_times),
                "rerank_time": max(data_rerank_times)
            })
        if car_group:
            res.append({
                "collection": "carKnowledge",
                "refs": car_group[:top_r],
                "search_time": max(car_search_times),
                "rerank_time": max(car_rerank_times)
            })
        if isc_group:
            res.append({
                "collection": "iscKnowledge",
                "refs": isc_group[:top_r],
                "search_time": sum(isc_search_times),
                "rerank_time": sum(isc_rerank_times)
            })

        return res

    def format_knowledge(self, reranked_docs):
        """格式化重排后的知识为字符串"""
        formatted_docs = []
        for i, doc in enumerate(reranked_docs):
            formatted_doc = f"\n检索结果{i+1}:\n"
            formatted_doc += f"{doc.get('content', '')}\n"
            formatted_docs.append(formatted_doc)
        self.logger.info(f"格式化后的知识, 共: {len(formatted_docs)}条")
        return "\n\n".join(formatted_docs), reranked_docs
    
    def deduplicate_by_docurl(self,docs):
        seen = set()
        deduped = []
        for doc in docs:
            url = doc.get("docUrl")
            if url not in seen:
                deduped.append(doc)
                seen.add(url)
        return deduped
    
    async def search_all_collections(
        self,
        query: str,
        user_id: str,
        timeout: Optional[float] = None,
        top_k: int = None,
        top_r: int = None,
        min_score: float = None,
        collections: Optional[List[str]] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        检索内容

        Args:
            query: 查询字符串
            user_id: 用户ID
            timeout: 超时时间
            top_k: 检索数量
            top_r: 重排数量
            min_score: 最小相似度
            collections: 要检索的集合类型列表，如 ["car", "hardware"] 或 ["data"]
                        如果为None或空列表，则检索所有集合
        """
        # 根据collections参数获取要检索的集合
        if collections:
            target_collections = get_collections_by_types(collections)
            self.logger.info(f"指定检索集合类型: {collections}, 对应集合数量: {len(target_collections)}")
        else:
            target_collections = ALL_SEARCH_COLLECTIONS
            self.logger.info(f"检索所有集合，集合数量: {len(target_collections)}")

        # 检索指定库并重排，分组
        grouped_res = await self._retrieve_and_rerank_all_collections(query, user_id, target_collections, top_k=top_k, top_r=top_r, min_score=min_score)
        self.logger.info(f"检索完成")
        # print(f"检索到的知识：{grouped_res}")
        yield grouped_res